package errors

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewAPIError(t *testing.T) {
	tests := []struct {
		name       string
		code       string
		message    string
		httpStatus int
		expected   APIError
	}{
		{
			name:       "basic error",
			code:       "BAD_REQUEST",
			message:    "Bad Request",
			httpStatus: 400,
			expected: APIError{
				Code:       "BAD_REQUEST",
				Message:    "Bad Request",
				HTTPStatus: 400,
			},
		},
		{
			name:       "server error",
			code:       "INTERNAL_SERVER_ERROR",
			message:    "Internal Server Error",
			httpStatus: 500,
			expected: APIError{
				Code:       "INTERNAL_SERVER_ERROR",
				Message:    "Internal Server Error",
				HTTPStatus: 500,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewAPIError(tt.code, tt.message, tt.httpStatus)
			if result.Code != tt.expected.Code {
				t.Errorf("Expected code %s, got %s", tt.expected.Code, result.Code)
			}
			if result.Message != tt.expected.Message {
				t.Errorf("Expected message %s, got %s", tt.expected.Message, result.Message)
			}
			if result.HTTPStatus != tt.expected.HTTPStatus {
				t.Errorf("Expected HTTPStatus %d, got %d", tt.expected.HTTPStatus, result.HTTPStatus)
			}
		})
	}
}

func TestAPIError_WithDetails(t *testing.T) {
	originalError := NewAPIError("BAD_REQUEST", "Bad Request", 400)
	newDetails := "Updated details with more information"
	
	updatedError := originalError.WithDetails(newDetails)
	
	if updatedError.Code != originalError.Code {
		t.Errorf("Expected code %s, got %s", originalError.Code, updatedError.Code)
	}
	if updatedError.Message != originalError.Message {
		t.Errorf("Expected message %s, got %s", originalError.Message, updatedError.Message)
	}
	if updatedError.Details != newDetails {
		t.Errorf("Expected details %v, got %v", newDetails, updatedError.Details)
	}
	
	// Ensure original error is modified (WithDetails modifies the original)
	if originalError.Details != newDetails {
		t.Errorf("Original error details should be modified by WithDetails")
	}
}

func TestAPIError_Error(t *testing.T) {
	apiError := NewAPIError("NOT_FOUND", "Not Found", 404)
	expected := "[NOT_FOUND] Not Found"
	
	result := apiError.Error()
	if result != expected {
		t.Errorf("Expected error string %s, got %s", expected, result)
	}
}

func TestWriteErrorResponse(t *testing.T) {
	tests := []struct {
		name           string
		apiError       *APIError
		expectedStatus int
	}{
		{
			name:           "bad request error",
			apiError:       NewAPIError("BAD_REQUEST", "Bad Request", http.StatusBadRequest).WithDetails("Invalid JSON"),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "internal server error",
			apiError:       NewAPIError("INTERNAL_SERVER_ERROR", "Internal Server Error", http.StatusInternalServerError).WithDetails("Database error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest(http.MethodGet, "/test", nil)
			
			WriteErrorResponse(w, r, tt.apiError)
			
			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}
			
			// Check content type
			contentType := w.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected content type application/json, got %s", contentType)
			}
			
			// Check response body
			var response ErrorResponse
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to unmarshal response: %v", err)
			}
			
			if response.Success != false {
				t.Errorf("Expected success false, got %v", response.Success)
			}
			if response.Error != tt.apiError.Message {
				t.Errorf("Expected error %s, got %s", tt.apiError.Message, response.Error)
			}
			if response.Code != tt.apiError.Code {
				t.Errorf("Expected code %s, got %s", tt.apiError.Code, response.Code)
			}
			if response.Details != tt.apiError.Details {
				t.Errorf("Expected details %v, got %v", tt.apiError.Details, response.Details)
			}
		})
	}
}

func TestWriteErrorResponse_InvalidJSON(t *testing.T) {
	// Test case where JSON encoding might fail (though unlikely with our simple struct)
	w := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodGet, "/test", nil)
	apiError := NewAPIError("TEST_ERROR", "Test Error", http.StatusInternalServerError).WithDetails("Test Details")
	
	WriteErrorResponse(w, r, apiError)
	
	// Should still work normally
	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status code %d, got %d", http.StatusInternalServerError, w.Code)
	}
	
	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected content type application/json, got %s", contentType)
	}
}

func TestErrorResponse_JSON(t *testing.T) {
	errorResp := ErrorResponse{
		Success: false,
		Error:   "Test Error",
		Code:    "TEST_ERROR",
		Details: "Test Details",
	}
	
	data, err := json.Marshal(errorResp)
	if err != nil {
		t.Fatalf("Failed to marshal ErrorResponse: %v", err)
	}
	
	var unmarshaled ErrorResponse
	if err := json.Unmarshal(data, &unmarshaled); err != nil {
		t.Fatalf("Failed to unmarshal ErrorResponse: %v", err)
	}
	
	if unmarshaled.Success != errorResp.Success {
		t.Errorf("Expected success %v, got %v", errorResp.Success, unmarshaled.Success)
	}
	if unmarshaled.Error != errorResp.Error {
		t.Errorf("Expected error %s, got %s", errorResp.Error, unmarshaled.Error)
	}
	if unmarshaled.Code != errorResp.Code {
		t.Errorf("Expected code %s, got %s", errorResp.Code, unmarshaled.Code)
	}
	if unmarshaled.Details != errorResp.Details {
		t.Errorf("Expected details %v, got %v", errorResp.Details, unmarshaled.Details)
	}
}