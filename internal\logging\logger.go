package logging

import (
	"context"
	"io"
	"os"
	"time"

	"github.com/sirupsen/logrus"
)

// <PERSON><PERSON> wraps logrus.Logger with additional functionality
type Logger struct {
	*logrus.Logger
}

// Fields represents structured logging fields
type Fields map[string]interface{}

// RequestMetrics contains request performance metrics
type RequestMetrics struct {
	Method        string        `json:"method"`
	Path          string        `json:"path"`
	StatusCode    int           `json:"status_code"`
	Duration      time.Duration `json:"duration_ms"`
	RequestSize   int64         `json:"request_size_bytes"`
	ResponseSize  int64         `json:"response_size_bytes"`
	UserAgent     string        `json:"user_agent,omitempty"`
	RemoteAddr    string        `json:"remote_addr,omitempty"`
	RequestID     string        `json:"request_id,omitempty"`
	ErrorCode     string        `json:"error_code,omitempty"`
	ErrorMessage  string        `json:"error_message,omitempty"`
}

// Config represents logging configuration
type Config struct {
	Level      string `yaml:"level" json:"level"`
	Format     string `yaml:"format" json:"format"` // json or text
	Output     string `yaml:"output" json:"output"` // stdout, stderr, or file path
	Timestamp  bool   `yaml:"timestamp" json:"timestamp"`
	Caller     bool   `yaml:"caller" json:"caller"`
	StackTrace bool   `yaml:"stack_trace" json:"stack_trace"`
}

// DefaultConfig returns default logging configuration
func DefaultConfig() *Config {
	return &Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     true,
		StackTrace: false,
	}
}

// NewLogger creates a new logger instance with the given configuration
func NewLogger(config *Config) (*Logger, error) {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		return nil, err
	}
	logger.SetLevel(level)

	// Set formatter
	switch config.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339Nano,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "caller",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: time.RFC3339,
			FullTimestamp:   config.Timestamp,
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339Nano,
		})
	}

	// Set output
	var output io.Writer
	switch config.Output {
	case "stdout":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// Assume it's a file path
		file, err := os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		output = file
	}
	logger.SetOutput(output)

	// Set caller reporting
	logger.SetReportCaller(config.Caller)

	return &Logger{Logger: logger}, nil
}

// WithFields creates a new logger entry with the given fields
func (l *Logger) WithFields(fields Fields) *logrus.Entry {
	return l.Logger.WithFields(logrus.Fields(fields))
}

// WithContext creates a new logger entry with context
func (l *Logger) WithContext(ctx context.Context) *logrus.Entry {
	return l.Logger.WithContext(ctx)
}

// WithError creates a new logger entry with error
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// LogRequest logs HTTP request with metrics
func (l *Logger) LogRequest(metrics *RequestMetrics) {
	fields := Fields{
		"method":         metrics.Method,
		"path":           metrics.Path,
		"status_code":    metrics.StatusCode,
		"duration_ms":    metrics.Duration.Milliseconds(),
		"request_size":   metrics.RequestSize,
		"response_size":  metrics.ResponseSize,
	}

	if metrics.UserAgent != "" {
		fields["user_agent"] = metrics.UserAgent
	}
	if metrics.RemoteAddr != "" {
		fields["remote_addr"] = metrics.RemoteAddr
	}
	if metrics.RequestID != "" {
		fields["request_id"] = metrics.RequestID
	}
	if metrics.ErrorCode != "" {
		fields["error_code"] = metrics.ErrorCode
	}
	if metrics.ErrorMessage != "" {
		fields["error_message"] = metrics.ErrorMessage
	}

	entry := l.WithFields(fields)

	// Log at different levels based on status code
	if metrics.StatusCode >= 500 {
		entry.Error("HTTP request completed with server error")
	} else if metrics.StatusCode >= 400 {
		entry.Warn("HTTP request completed with client error")
	} else {
		entry.Info("HTTP request completed successfully")
	}
}

// LogError logs an error with additional context
func (l *Logger) LogError(err error, message string, fields Fields) {
	entry := l.WithError(err)
	if fields != nil {
		entry = entry.WithFields(logrus.Fields(fields))
	}
	entry.Error(message)
}

// LogPerformance logs performance metrics
func (l *Logger) LogPerformance(operation string, duration time.Duration, fields Fields) {
	allFields := Fields{
		"operation":   operation,
		"duration_ms": duration.Milliseconds(),
	}
	
	// Merge additional fields
	for k, v := range fields {
		allFields[k] = v
	}

	entry := l.WithFields(allFields)
	
	// Log at different levels based on duration
	if duration > 5*time.Second {
		entry.Warn("Slow operation detected")
	} else if duration > 1*time.Second {
		entry.Info("Operation completed")
	} else {
		entry.Debug("Operation completed")
	}
}

// LogMemoryUsage logs memory usage statistics
func (l *Logger) LogMemoryUsage(operation string, beforeMB, afterMB, allocatedMB float64) {
	fields := Fields{
		"operation":     operation,
		"memory_before": beforeMB,
		"memory_after":  afterMB,
		"memory_allocated": allocatedMB,
		"memory_freed":  beforeMB - afterMB,
	}

	entry := l.WithFields(fields)
	
	// Log at different levels based on memory usage
	if allocatedMB > 100 {
		entry.Warn("High memory allocation detected")
	} else {
		entry.Debug("Memory usage logged")
	}
}

// Global logger instance
var defaultLogger *Logger

// InitGlobalLogger initializes the global logger
func InitGlobalLogger(config *Config) error {
	logger, err := NewLogger(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// GetLogger returns the global logger instance
func GetLogger() *Logger {
	if defaultLogger == nil {
		// Initialize with default config if not already initialized
		logger, _ := NewLogger(DefaultConfig())
		defaultLogger = logger
	}
	return defaultLogger
}

// Convenience functions for global logger
func Info(msg string, fields ...Fields) {
	logger := GetLogger()
	if len(fields) > 0 {
		logger.WithFields(fields[0]).Info(msg)
	} else {
		logger.Info(msg)
	}
}

func Error(msg string, err error, fields ...Fields) {
	logger := GetLogger()
	entry := logger.WithError(err)
	if len(fields) > 0 {
		entry = entry.WithFields(logrus.Fields(fields[0]))
	}
	entry.Error(msg)
}

func Warn(msg string, fields ...Fields) {
	logger := GetLogger()
	if len(fields) > 0 {
		logger.WithFields(fields[0]).Warn(msg)
	} else {
		logger.Warn(msg)
	}
}

func Debug(msg string, fields ...Fields) {
	logger := GetLogger()
	if len(fields) > 0 {
		logger.WithFields(fields[0]).Debug(msg)
	} else {
		logger.Debug(msg)
	}
}