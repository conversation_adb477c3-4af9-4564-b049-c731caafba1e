package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
	"qr-background-api/internal/config"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/timeout"
)

// CleanupHandler handles cleanup requests for old images
type CleanupHandler struct {
	metadataManager interfaces.MetadataManager
	storageManager  interfaces.StorageManager
	config          *config.Config
}

// NewCleanupHandler creates a new CleanupHandler instance
func NewCleanupHandler(metadataManager interfaces.MetadataManager, storageManager interfaces.StorageManager, cfg *config.Config) *CleanupHandler {
	return &CleanupHandler{
		metadataManager: metadataManager,
		storageManager:  storageManager,
		config:          cfg,
	}
}

// CleanupRequest represents the cleanup request structure
type CleanupRequest struct {
	BatchSize int `json:"batch_size,omitempty"` // Number of images to process within current folder
	MaxAge    int `json:"max_age_hours,omitempty"` // Age threshold in hours for cleanup
}

// CleanupResponse represents the cleanup response structure
type CleanupResponse struct {
	Success           bool                        `json:"success"`
	Message           string                      `json:"message,omitempty"`
	ProcessedFolder   int                         `json:"processed_folder"`
	ImagesProcessed   int                         `json:"images_processed"`
	ImagesMovedToCloud int                        `json:"images_moved_to_cloud"`
	ImagesDeleted     int                         `json:"images_deleted"`
	NextFolder        int                         `json:"next_folder"`
	ProcessingTimeMs  int64                       `json:"processing_time_ms"`
	ProcessedImages   []ProcessedImageInfo        `json:"processed_images,omitempty"`
}

// ProcessedImageInfo contains information about a processed image
type ProcessedImageInfo struct {
	ImagePath string `json:"image_path"`
	Action    string `json:"action"` // "moved_to_cloud" or "deleted" or "skipped"
	Reason    string `json:"reason,omitempty"`
}

// Error definitions for cleanup operations
var (
	ErrCleanupInvalidRequest = errors.NewAPIError(
		"CLEANUP_INVALID_REQUEST",
		"Request validation failed",
		400,
	)
	ErrCleanupInvalidBatchSize = errors.NewAPIError(
		"CLEANUP_INVALID_BATCH_SIZE",
		"Batch size must be between 1 and 1000",
		400,
	)
	ErrCleanupInvalidMaxAge = errors.NewAPIError(
		"CLEANUP_INVALID_MAX_AGE",
		"Max age must be between 1 and 8760 hours (1 year)",
		400,
	)
	ErrCleanupProcessingFailed = errors.NewAPIError(
		"CLEANUP_PROCESSING_FAILED",
		"Internal processing error",
		500,
	)
	ErrCleanupMetadataFailed = errors.NewAPIError(
		"CLEANUP_METADATA_FAILED",
		"Failed to read or update metadata",
		500,
	)
)

// ServeHTTP implements the http.Handler interface for cleanup endpoint
func (h *CleanupHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout.GetManager().GetConfig().Cleanup)
	defer cancel()
	r = r.WithContext(ctx)

	// Initialize logger with request context
	logger := logging.GetLogger().WithFields(logging.Fields{
		"handler":    "cleanup",
		"method":     r.Method,
		"path":       r.URL.Path,
		"user_agent": r.UserAgent(),
		"remote_ip":  r.RemoteAddr,
	})

	logger.Info("Processing cleanup request")
	startTime := time.Now()

	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	SetCORSHeaders(w)

	// Handle preflight requests
	if HandlePreflight(w, r) {
		logger.Debug("Handled preflight request")
		return
	}

	// Only allow POST method
	if r.Method != http.MethodPost {
		logger.WithField("method", r.Method).Warn("Method not allowed")
		errors.WriteErrorResponse(w, r, errors.NewAPIError(
			"METHOD_NOT_ALLOWED",
			"Method not allowed",
			405,
		))
		return
	}

	// Parse request
	var req CleanupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.WithError(err).Error("Failed to parse cleanup request")
		errors.WriteErrorResponse(w, r, ErrCleanupInvalidRequest.WithDetails(fmt.Sprintf("JSON decode error: %v", err)))
		return
	}

	logger.WithFields(logrus.Fields{
		"batch_size": req.BatchSize,
		"max_age":    req.MaxAge,
	}).Debug("Parsed cleanup request")

	// Validate request
	if apiErr := h.validateRequest(&req); apiErr != nil {
		logger.WithFields(logrus.Fields{
			"error_code": apiErr.Code,
			"error_msg":  apiErr.Message,
		}).Warn("Cleanup request validation failed")
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	// Set defaults from config if not provided
	if req.BatchSize == 0 {
		req.BatchSize = h.config.Cleanup.BatchSize
	}
	if req.MaxAge == 0 {
		req.MaxAge = h.config.Cleanup.MaxAgeHours
	}

	// Process cleanup
	response, err := h.processCleanup(ctx, &req, logger)
	if err != nil {
		logger.WithError(err).Error("Cleanup processing failed")
		errors.WriteErrorResponse(w, r, ErrCleanupProcessingFailed.WithDetails(err.Error()))
		return
	}

	// Set processing time
	processingTime := time.Since(startTime)
	response.ProcessingTimeMs = processingTime.Milliseconds()
	response.Success = true
	response.Message = "Cleanup completed successfully"

	logger.WithFields(logging.Fields{
		"processing_time_ms":    response.ProcessingTimeMs,
		"processed_folder":      response.ProcessedFolder,
		"images_processed":      response.ImagesProcessed,
		"images_moved_to_cloud": response.ImagesMovedToCloud,
		"images_deleted":        response.ImagesDeleted,
	}).Info("Cleanup request completed successfully")

	// Write success response
	if err := WriteJSONResponse(w, http.StatusOK, response); err != nil {
		logger.WithError(err).Error("Failed to write cleanup response")
	}
}

// validateRequest validates the cleanup request parameters
func (h *CleanupHandler) validateRequest(req *CleanupRequest) *errors.APIError {
	// Validate batch size
	if req.BatchSize < 0 || req.BatchSize > 1000 {
		return &ErrCleanupInvalidBatchSize
	}

	// Validate max age
	if req.MaxAge < 0 || req.MaxAge > 8760 { // Max 1 year
		return &ErrCleanupInvalidMaxAge
	}

	return nil
}

// processCleanup performs the actual cleanup processing
func (h *CleanupHandler) processCleanup(ctx context.Context, req *CleanupRequest, logger *logrus.Entry) (*CleanupResponse, error) {
	// Get the last processed folder
	lastProcessedFolder, err := h.metadataManager.GetLastProcessedFolder()
	if err != nil {
		logger.WithError(err).Error("Failed to get last processed folder")
		return nil, fmt.Errorf("failed to get last processed folder: %w", err)
	}

	// Determine next folder to process
	currentFolder := lastProcessedFolder + 1
	if currentFolder > h.config.Cleanup.MaxFolders {
		currentFolder = 1 // Wrap around to start from folder 1
	}

	// Get images eligible for cleanup from current folder
	maxAge := time.Duration(req.MaxAge) * time.Hour
	logger.WithFields(logrus.Fields{
		"current_folder": currentFolder,
		"max_age_hours": req.MaxAge,
		"batch_size":    req.BatchSize,
	}).Debug("Getting images for cleanup")

	images, err := h.metadataManager.GetImagesForCleanup(currentFolder, maxAge, req.BatchSize)
	if err != nil {
		logger.WithError(err).WithField("folder", currentFolder).Error("Failed to get images for cleanup")
		return nil, fmt.Errorf("failed to get images for cleanup: %w", err)
	}

	logger.WithField("images_found", len(images)).Info("Found images for cleanup processing")

	// Initialize response
	response := &CleanupResponse{
		ProcessedFolder: currentFolder,
		NextFolder:      currentFolder + 1,
		ProcessedImages: make([]ProcessedImageInfo, 0, len(images)),
	}

	// Handle wrap around for next folder
	if response.NextFolder > h.config.Cleanup.MaxFolders {
		response.NextFolder = 1
	}

	// Process each eligible image
	for _, image := range images {
		processedInfo := ProcessedImageInfo{
			ImagePath: image.ImagePath,
		}

		// Check context for cancellation
		select {
		case <-ctx.Done():
			logger.Warn("Cleanup processing cancelled due to timeout")
			return response, ctx.Err()
		default:
		}

		// Determine action based on image location and age
		if image.Location == "local" {
			// Try to move to cloud storage
			logger.WithField("image_path", image.ImagePath).Debug("Moving image to cloud")
			if err := h.storageManager.MoveToCloud(image.ImagePath); err != nil {
				logger.WithError(err).WithField("image_path", image.ImagePath).Warn("Failed to move image to cloud")
				processedInfo.Action = "skipped"
				processedInfo.Reason = fmt.Sprintf("Failed to move to cloud: %v", err)
			} else {
				// Update metadata to reflect cloud location
				image.Location = "cloud"
				if metaErr := h.metadataManager.SaveImageMetadata(image.ImagePath, image); metaErr != nil {
					logger.WithError(metaErr).WithField("image_path", image.ImagePath).Error("Failed to update metadata after cloud move")
				}
				processedInfo.Action = "moved_to_cloud"
				response.ImagesMovedToCloud++
				logger.WithField("image_path", image.ImagePath).Info("Successfully moved image to cloud")
			}
		} else if image.Location == "cloud" {
			// Image is already in cloud, can be deleted from local storage
			logger.WithField("image_path", image.ImagePath).Debug("Deleting image from local storage")
			if err := h.storageManager.DeleteImage(image.ImagePath); err != nil {
				logger.WithError(err).WithField("image_path", image.ImagePath).Warn("Failed to delete image")
				processedInfo.Action = "skipped"
				processedInfo.Reason = fmt.Sprintf("Failed to delete: %v", err)
			} else {
				// Delete metadata as well
				if metaErr := h.metadataManager.DeleteImageMetadata(image.ImagePath); metaErr != nil {
					logger.WithError(metaErr).WithField("image_path", image.ImagePath).Error("Failed to delete metadata after image deletion")
				}
				processedInfo.Action = "deleted"
				response.ImagesDeleted++
				logger.WithField("image_path", image.ImagePath).Info("Successfully deleted image")
			}
		} else {
			logger.WithFields(logging.Fields{
				"image_path": image.ImagePath,
				"location":   image.Location,
			}).Warn("Skipping image with unknown location")
			processedInfo.Action = "skipped"
			processedInfo.Reason = "Unknown location"
		}

		response.ProcessedImages = append(response.ProcessedImages, processedInfo)
		response.ImagesProcessed++
	}

	// Update last processed folder
	logger.WithField("folder", currentFolder).Debug("Updating last processed folder")
	if err := h.metadataManager.SetLastProcessedFolder(currentFolder); err != nil {
		logger.WithError(err).WithField("folder", currentFolder).Error("Failed to update last processed folder")
		return nil, fmt.Errorf("failed to update last processed folder: %w", err)
	}

	return response, nil
}