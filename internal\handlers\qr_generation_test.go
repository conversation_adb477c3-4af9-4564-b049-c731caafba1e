package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/interfaces"
)

// MockWorkerPool implements interfaces.WorkerPool for testing
type MockWorkerPool struct {
	isRunning        bool
	shouldTimeout    bool
	shouldFail       bool
	errorMessage     string
	responseData     []byte
	submittedJobs    []interfaces.QRJob
	processingDelay  time.Duration
}

func (m *MockWorkerPool) Submit(job interfaces.QRJob) <-chan interfaces.QRResult {
	resultChan := make(chan interfaces.QRResult, 1)
	m.submittedJobs = append(m.submittedJobs, job)
	
	go func() {
		if m.processingDelay > 0 {
			time.Sleep(m.processingDelay)
		}
		
		if m.shouldTimeout {
			// Don't send result to simulate timeout
			return
		}
		
		if m.shouldFail {
			resultChan <- interfaces.QRResult{
				ImageData: nil,
				Error:     fmt.<PERSON><PERSON><PERSON>(m.errorMessage),
			}
		} else {
			resultChan <- interfaces.QRResult{
				ImageData: m.responseData,
				Error:     nil,
			}
		}
	}()
	
	return resultChan
}

func (m *MockWorkerPool) Start(workerCount int) error {
	m.isRunning = true
	return nil
}

func (m *MockWorkerPool) Stop() error {
	m.isRunning = false
	return nil
}

func (m *MockWorkerPool) GetStats() interfaces.WorkerPoolStats {
	return interfaces.WorkerPoolStats{}
}

func (m *MockWorkerPool) IsRunning() bool {
	return m.isRunning
}

func TestNewQRGenerationHandler(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: true}
	
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	if handler == nil {
		t.Fatal("NewQRGenerationHandler() returned nil")
	}
	
	if handler.workerPool != mockWorkerPool {
		t.Error("Handler worker pool not set correctly")
	}
	
	if handler.config != cfg {
		t.Error("Handler config not set correctly")
	}
}

func TestQRGenerationHandler_ServeHTTP_Success(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-image-data"),
	}
	
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	reqBody := `{
		"data": "https://example.com",
		"image_path": "images/1/test.jpg",
		"x": 10,
		"y": 20,
		"width": 100,
		"height": 100,
		"output_format": "png"
	}`
	
	req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}
	
	if w.Header().Get("Content-Type") != "image/png" {
		t.Errorf("Expected Content-Type image/png, got %s", w.Header().Get("Content-Type"))
	}
	
	if w.Body.String() != "fake-image-data" {
		t.Errorf("Expected response body 'fake-image-data', got %s", w.Body.String())
	}
	
	// Verify job was submitted correctly
	if len(mockWorkerPool.submittedJobs) != 1 {
		t.Errorf("Expected 1 submitted job, got %d", len(mockWorkerPool.submittedJobs))
	}
	
	job := mockWorkerPool.submittedJobs[0]
	if job.Request.Data != "https://example.com" {
		t.Errorf("Expected job data 'https://example.com', got %s", job.Request.Data)
	}
	if job.Request.ImagePath != "images/1/test.jpg" {
		t.Errorf("Expected job image path 'images/1/test.jpg', got %s", job.Request.ImagePath)
	}
}

func TestQRGenerationHandler_ServeHTTP_MethodNotAllowed(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: true}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	req := httptest.NewRequest(http.MethodGet, "/generate-qr", nil)
	w := httptest.NewRecorder()
	
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusMethodNotAllowed {
		t.Errorf("Expected status %d, got %d", http.StatusMethodNotAllowed, w.Code)
	}
}

func TestQRGenerationHandler_ServeHTTP_WorkerPoolNotRunning(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: false}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	reqBody := `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100}`
	req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusServiceUnavailable {
		t.Errorf("Expected status %d, got %d", http.StatusServiceUnavailable, w.Code)
	}
	
	var response ErrorResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}
	
	if response.Success {
		t.Error("Expected success to be false")
	}
	if !strings.Contains(response.Error, "Service temporarily unavailable") {
		t.Errorf("Expected error message about service unavailable, got %s", response.Error)
	}
}

func TestQRGenerationHandler_ServeHTTP_InvalidJSON(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: true}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestQRGenerationHandler_ServeHTTP_ValidationErrors(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: true}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "missing data",
			requestBody:    `{"image_path": "test.jpg", "width": 100, "height": 100}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "QR data is required",
		},
		{
			name:           "missing image path",
			requestBody:    `{"data": "test", "width": 100, "height": 100}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Image path is required",
		},
		{
			name:           "invalid width",
			requestBody:    `{"data": "test", "image_path": "test.jpg", "width": 0, "height": 100}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid QR dimensions",
		},
		{
			name:           "invalid height",
			requestBody:    `{"data": "test", "image_path": "test.jpg", "width": 100, "height": -1}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid QR dimensions",
		},
		{
			name:           "negative x position",
			requestBody:    `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100, "x": -1}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid QR position",
		},
		{
			name:           "negative y position",
			requestBody:    `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100, "y": -1}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid QR position",
		},
		{
			name:           "invalid output format",
			requestBody:    `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100, "output_format": "gif"}`,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid output format",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(tt.requestBody))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}
			
			var response ErrorResponse
			if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}
			
			if !strings.Contains(response.Error, tt.expectedError) {
				t.Errorf("Expected error message to contain %q, got %q", tt.expectedError, response.Error)
			}
		})
	}
}

func TestQRGenerationHandler_ServeHTTP_ProcessingErrors(t *testing.T) {
	cfg := &config.Config{}
	
	tests := []struct {
		name           string
		errorMessage   string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "image not found",
			errorMessage:   "image not found: test.jpg",
			expectedStatus: http.StatusNotFound,
			expectedError:  "Background image not found",
		},
		{
			name:           "dimensions exceed boundaries",
			errorMessage:   "dimensions exceed background boundaries",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "QR dimensions exceed background boundaries",
		},
		{
			name:           "general processing error",
			errorMessage:   "some other error",
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "QR generation failed",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockWorkerPool := &MockWorkerPool{
				isRunning:    true,
				shouldFail:   true,
				errorMessage: tt.errorMessage,
			}
			
			handler := NewQRGenerationHandler(mockWorkerPool, cfg)
			
			reqBody := `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100}`
			req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(reqBody))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}
			
			var response ErrorResponse
			if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}
			
			if !strings.Contains(response.Error, tt.expectedError) {
				t.Errorf("Expected error message to contain %q, got %q", tt.expectedError, response.Error)
			}
		})
	}
}

func TestQRGenerationHandler_ServeHTTP_Timeout(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{
		isRunning:     true,
		shouldTimeout: true,
	}
	
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	reqBody := `{"data": "test", "image_path": "test.jpg", "width": 100, "height": 100}`
	req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
	}
	
	var response ErrorResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}
	
	if !strings.Contains(response.Error, "QR generation timeout") {
		t.Errorf("Expected timeout error message, got %s", response.Error)
	}
}

func TestQRGenerationHandler_ServeHTTP_OutputFormats(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-image-data"),
	}
	
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	tests := []struct {
		name            string
		outputFormat    string
		expectedType    string
	}{
		{"jpeg format", "jpeg", "image/jpeg"},
		{"jpg format", "jpg", "image/jpeg"},
		{"png format", "png", "image/png"},
		{"webp format", "webp", "image/webp"},
		{"default format", "", "image/png"},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reqBody := fmt.Sprintf(`{
				"data": "test",
				"image_path": "test.jpg",
				"width": 100,
				"height": 100,
				"output_format": "%s"
			}`, tt.outputFormat)
			
			req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(reqBody))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			
			if w.Code != http.StatusOK {
				t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
			}
			
			if w.Header().Get("Content-Type") != tt.expectedType {
				t.Errorf("Expected Content-Type %s, got %s", tt.expectedType, w.Header().Get("Content-Type"))
			}
		})
	}
}

func TestQRGenerationHandler_ServeHTTP_CORS(t *testing.T) {
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{isRunning: true}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	// Test OPTIONS request (preflight)
	req := httptest.NewRequest(http.MethodOptions, "/generate-qr", nil)
	w := httptest.NewRecorder()
	
	handler.ServeHTTP(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d for OPTIONS request, got %d", http.StatusOK, w.Code)
	}
	
	// Check CORS headers
	if w.Header().Get("Access-Control-Allow-Origin") != "*" {
		t.Error("Expected Access-Control-Allow-Origin header")
	}
	if w.Header().Get("Access-Control-Allow-Methods") == "" {
		t.Error("Expected Access-Control-Allow-Methods header")
	}
}

func TestQRGenerationHandler_validateRequest(t *testing.T) {
	handler := &QRGenerationHandler{}
	
	tests := []struct {
		name        string
		request     QRGenerationRequest
		expectError bool
		errorCode   int
	}{
		{
			name: "valid request",
			request: QRGenerationRequest{
				Data:         "test",
				ImagePath:    "test.jpg",
				Width:        100,
				Height:       100,
				OutputFormat: "png",
			},
			expectError: false,
		},
		{
			name: "empty data",
			request: QRGenerationRequest{
				ImagePath: "test.jpg",
				Width:     100,
				Height:    100,
			},
			expectError: true,
			errorCode:   400,
		},
		{
			name: "empty image path",
			request: QRGenerationRequest{
				Data:   "test",
				Width:  100,
				Height: 100,
			},
			expectError: true,
			errorCode:   400,
		},
		{
			name: "default format applied",
			request: QRGenerationRequest{
				Data:      "test",
				ImagePath: "test.jpg",
				Width:     100,
				Height:    100,
				// OutputFormat empty - should default to png
			},
			expectError: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := handler.validateRequest(&tt.request)
			
			if tt.expectError {
				if err == nil {
					t.Error("Expected validation error but got none")
				} else if err.Code != tt.errorCode {
					t.Errorf("Expected error code %d, got %d", tt.errorCode, err.Code)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected validation error: %v", err)
				}
				// Check default format was applied
				if tt.request.OutputFormat == "" && tt.request.OutputFormat != "png" {
					// Note: validateRequest modifies the request in place
					// This test would need to be adjusted based on actual implementation
				}
			}
		})
	}
}