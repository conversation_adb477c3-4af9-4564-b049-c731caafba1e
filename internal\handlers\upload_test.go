package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/storage"
	"qr-background-api/internal/worker"
)

// Mock implementations for testing
type mockBufferPool struct{}

func (m *mockBufferPool) Get() *bytes.Buffer {
	return &bytes.Buffer{}
}

func (m *mockBufferPool) Put(buf *bytes.Buffer) {
	// Do nothing
}

type mockWorkerPool struct {
	shouldFail bool
}

func (m *mockWorkerPool) Submit(job interfaces.QRJob) <-chan interfaces.QRResult {
	resultChan := make(chan interfaces.QRResult, 1)
	if m.shouldFail {
		go func() {
			resultChan <- interfaces.QRResult{
				Error: fmt.<PERSON>rf("worker pool error"),
			}
			close(resultChan)
		}()
		return resultChan
	}
	// Simulate async processing
	go func() {
		time.Sleep(10 * time.Millisecond)
		resultChan <- interfaces.QRResult{
			ImageData: []byte("mock result"),
			Error: nil,
		}
		close(resultChan)
	}()
	return resultChan
}

func (m *mockWorkerPool) Stop() {}

type mockStorageManager struct {
	shouldFail bool
}

func (m *mockStorageManager) SaveFile(ctx context.Context, filename string, data io.Reader, metadata map[string]string) error {
	if m.shouldFail {
		return fmt.Errorf("storage error")
	}
	return nil
}



func setupTestUploadHandler(shouldFailWorker, shouldFailStorage bool) *UploadHandler {
	// Initialize logger for testing
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)

	// Create test config
	testConfig := &config.Config{
		Upload: config.UploadConfig{
			MaxFileSize:    10 * 1024 * 1024, // 10MB
			AllowedTypes:   []string{"image/jpeg", "image/png", "image/gif"},
			UploadDir:      "/tmp/test-uploads",
			MaxConcurrent:  5,
			Timeout:        30,
			RetryAttempts:  3,
			RetryDelay:     1,
		},
	}

	return NewUploadHandler(
		testConfig,
		&mockBufferPool{},
		&mockWorkerPool{shouldFail: shouldFailWorker},
		&mockStorageManager{shouldFail: shouldFailStorage},
	)
}

// Helper function to create multipart form data
func createMultipartFormWithContentType(fieldName, fileName, contentType string, content []byte) (*bytes.Buffer, string, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Create form file with custom content type
	h := make(map[string][]string)
	h["Content-Type"] = []string{contentType}
	part, err := writer.CreatePart(map[string][]string{
		"Content-Disposition": {fmt.Sprintf(`form-data; name="%s"; filename="%s"`, fieldName, fileName)},
		"Content-Type":        {contentType},
	})
	if err != nil {
		return nil, "", err
	}

	_, err = part.Write(content)
	if err != nil {
		return nil, "", err
	}

	err = writer.Close()
	if err != nil {
		return nil, "", err
	}

	return body, writer.FormDataContentType(), nil
}

// Helper function to create multipart form data (original version for backward compatibility)
func createMultipartForm(fieldName, fileName, contentType string, content []byte) (*bytes.Buffer, string, error) {
	return createMultipartFormWithContentType(fieldName, fileName, contentType, content)
}

// Test successful image upload
func TestUploadHandler_Success(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	// Create test image data (simple JPEG header)
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	// Create multipart form
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	// Create request
	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)

	// Create response recorder
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusAccepted {
		t.Errorf("Expected status %d, got %d", http.StatusAccepted, w.Code)
	}

	// Parse response
	var response map[string]interface{}
	err = json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Verify response
	if response["success"] != true {
		t.Error("Expected success to be true")
	}
	if response["message"] == nil {
		t.Error("Expected message in response")
	}
	if response["filename"] == nil {
		t.Error("Expected filename in response")
	}
}

// Test upload with invalid method
func TestUploadHandler_InvalidMethod(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	req := httptest.NewRequest(http.MethodGet, "/upload", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusMethodNotAllowed {
		t.Errorf("Expected status %d, got %d", http.StatusMethodNotAllowed, w.Code)
	}

	// Check error response format
	var response errors.APIError
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode error response: %v", err)
	}

	if response.Success {
		t.Error("Expected success=false")
	}
	if response.Error == "" {
		t.Error("Expected error message")
	}
}

// Test upload without file
func TestUploadHandler_NoFile(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	// Create request without file
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	writer.Close()

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}

	var response errors.APIError
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode error response: %v", err)
	}

	if response.Success {
		t.Error("Expected success=false")
	}
	if !strings.Contains(response.Error, "file") {
		t.Error("Expected file-related error message")
	}
}

// Test upload with invalid image format
func TestUploadHandler_InvalidFormat(t *testing.T) {
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			MaxFileSize: 10 << 20,
		},
	}
	mockStorage := &MockStorageManager{}
	handler := NewUploadHandler(mockStorage, cfg)

	// Create test content with invalid format
	testContent := []byte("This is not an image")
	body, contentType, err := createMultipartForm("file", "test.txt", "text/plain", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}

	var apiError APIError
	err = json.NewDecoder(w.Body).Decode(&apiError)
	if err != nil {
		t.Fatalf("Failed to decode error response: %v", err)
	}

	if !strings.Contains(apiError.Message, "Invalid image format") {
		t.Errorf("Expected invalid format error, got: %s", apiError.Message)
	}
}

// Test upload with file too large
func TestUploadHandler_FileTooLarge(t *testing.T) {
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			MaxFileSize: 1000, // Very small limit
		},
	}
	mockStorage := &MockStorageManager{}
	handler := NewUploadHandler(mockStorage, cfg)

	// Create large test content
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 2000)...) // Larger than limit
	body, contentType, err := createMultipartForm("file", "large.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

// Test storage manager error
func TestUploadHandler_StorageError(t *testing.T) {
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			MaxFileSize: 10 << 20,
		},
	}
	mockStorage := &MockStorageManager{
		shouldFailSave: true,
		saveError:      fmt.Errorf("storage failure"),
	}
	handler := NewUploadHandler(mockStorage, cfg)

	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
	}

	// Test format validation error from storage
	mockStorage2 := &MockStorageManager{
		shouldFailSave: true,
		saveError:      fmt.Errorf("unsupported image format: image/gif"),
	}
	handler2 := NewUploadHandler(mockStorage2, cfg)

	req2 := httptest.NewRequest(http.MethodPost, "/upload", body)
	req2.Header.Set("Content-Type", contentType)
	w2 := httptest.NewRecorder()

	handler2.ServeHTTP(w2, req2)

	if w2.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d for format error, got %d", http.StatusBadRequest, w2.Code)
	}
}

// Test different image formats
func TestUploadHandler_DifferentFormats(t *testing.T) {
	testCases := []struct {
		name        string
		fileName    string
		contentType string
		header      []byte
		expectError bool
	}{
		{
			name:        "JPEG",
			fileName:    "test.jpg",
			contentType: "image/jpeg",
			header:      []byte{0xFF, 0xD8, 0xFF, 0xE0},
			expectError: false,
		},
		{
			name:        "PNG",
			fileName:    "test.png",
			contentType: "image/png",
			header:      []byte{0x89, 0x50, 0x4E, 0x47},
			expectError: false,
		},
		{
			name:        "WebP",
			fileName:    "test.webp",
			contentType: "image/webp",
			header:      []byte{0x52, 0x49, 0x46, 0x46},
			expectError: false,
		},
		{
			name:        "GIF (unsupported)",
			fileName:    "test.gif",
			contentType: "image/gif",
			header:      []byte{0x47, 0x49, 0x46, 0x38},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cfg := &config.Config{
				Storage: struct {
					LocalPath   string `yaml:"local_path"`
					CloudBucket string `yaml:"cloud_bucket"`
					MaxFileSize int64  `yaml:"max_file_size"`
				}{
					MaxFileSize: 10 << 20,
				},
			}
			
			// Configure mock storage based on expected result
			mockStorage := &MockStorageManager{}
			if tc.expectError {
				// For unsupported formats, mock storage should also fail
				mockStorage.shouldFailSave = true
				mockStorage.saveError = fmt.Errorf("unsupported image format: %s", tc.contentType)
			}
			
			handler := NewUploadHandler(mockStorage, cfg)

			testContent := append(tc.header, make([]byte, 1000)...)
			body, contentType, err := createMultipartForm("file", tc.fileName, tc.contentType, testContent)
			if err != nil {
				t.Fatalf("Failed to create multipart form: %v", err)
			}

			req := httptest.NewRequest(http.MethodPost, "/upload", body)
			req.Header.Set("Content-Type", contentType)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			if tc.expectError {
				if w.Code != http.StatusBadRequest {
					t.Errorf("Expected status %d for %s, got %d", http.StatusBadRequest, tc.name, w.Code)
				}
			} else {
				if w.Code != http.StatusOK {
					t.Errorf("Expected status %d for %s, got %d", http.StatusOK, tc.name, w.Code)
					t.Logf("Response body: %s", w.Body.String())
				}
			}
		})
	}
}

// Test helper functions
func TestFormatFileSize(t *testing.T) {
	testCases := []struct {
		bytes    int64
		expected string
	}{
		{512, "512 B"},
		{1024, "1.0 KB"},
		{1536, "1.5 KB"},
		{1048576, "1.0 MB"},
		{10485760, "10.0 MB"},
	}

	for _, tc := range testCases {
		result := formatFileSize(tc.bytes)
		if result != tc.expected {
			t.Errorf("formatFileSize(%d) = %s, expected %s", tc.bytes, result, tc.expected)
		}
	}
}

// Test isValidImageFormat
// Test worker pool error
func TestUploadHandler_WorkerPoolError(t *testing.T) {
	handler := setupTestUploadHandler(true, false) // Worker pool will fail

	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
	}

	var response errors.APIError
	err = json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode error response: %v", err)
	}

	if response.Success {
		t.Error("Expected success=false")
	}
}

// Test context timeout
func TestUploadHandler_ContextTimeout(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)

	// Create context with very short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
	defer cancel()
	req = req.WithContext(ctx)

	w := httptest.NewRecorder()
	handler.ServeHTTP(w, req)

	// Should handle timeout gracefully
	if w.Code != http.StatusRequestTimeout {
		t.Errorf("Expected status %d, got %d", http.StatusRequestTimeout, w.Code)
	}
}

// Test logging integration
func TestUploadHandler_LoggingIntegration(t *testing.T) {
	// Test that logging is properly integrated
	var buf bytes.Buffer
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)
	logger := logging.GetLogger()
	logger.Logger.SetOutput(&buf)

	handler := setupTestUploadHandler(false, false)

	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	// Check that logs were generated
	logOutput := buf.String()
	if !strings.Contains(logOutput, "Upload request started") {
		t.Error("Expected upload start log entry")
	}
}

// Test error response format
func TestUploadHandler_ErrorResponseFormat(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	// Test error response format
	req := httptest.NewRequest(http.MethodGet, "/upload", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	var response errors.APIError
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to parse error response: %v", err)
	}

	// Check error response structure
	if response.Success {
		t.Error("Expected success=false in error response")
	}
	if response.Error == "" {
		t.Error("Expected error message in response")
	}
	if response.Code == "" {
		t.Error("Expected error code in response")
	}
	if response.Timestamp.IsZero() {
		t.Error("Expected timestamp in error response")
	}
}

// Test concurrent requests
func TestUploadHandler_ConcurrentRequests(t *testing.T) {
	handler := setupTestUploadHandler(false, false)

	// Test concurrent uploads
	const numRequests = 10
	results := make(chan int, numRequests)

	for i := 0; i < numRequests; i++ {
		go func(id int) {
			jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
			testContent := append(jpegHeader, make([]byte, 1000)...)
			body, contentType, err := createMultipartForm("file", fmt.Sprintf("test%d.jpg", id), "image/jpeg", testContent)
			if err != nil {
				results <- 0
				return
			}

			req := httptest.NewRequest(http.MethodPost, "/upload", body)
			req.Header.Set("Content-Type", contentType)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			results <- w.Code
		}(i)
	}

	// Collect results
	successCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-results
		if code == http.StatusAccepted {
			successCount++
		}
	}

	// All requests should succeed
	if successCount != numRequests {
		t.Errorf("Expected %d successful requests, got %d", numRequests, successCount)
	}
}

// Benchmark upload handler
func BenchmarkUploadHandler(b *testing.B) {
	handler := setupTestUploadHandler(false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
		testContent := append(jpegHeader, make([]byte, 1000)...)
		body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
		if err != nil {
			b.Fatalf("Failed to create multipart form: %v", err)
		}

		req := httptest.NewRequest(http.MethodPost, "/upload", body)
		req.Header.Set("Content-Type", contentType)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
	}
}