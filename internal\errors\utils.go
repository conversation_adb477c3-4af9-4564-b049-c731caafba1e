package errors

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strings"

	"qr-background-api/internal/logging"
)

// ErrorResponse represents the JSON structure for error responses
type ErrorResponse struct {
	Success   bool        `json:"success"`
	Error     string      `json:"error"`
	Code      string      `json:"code"`
	Details   interface{} `json:"details,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
	Timestamp string      `json:"timestamp,omitempty"`
}

// ValidationError represents a field validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

// Error implements the error interface for ValidationErrors
func (ve ValidationErrors) Error() string {
	var messages []string
	for _, err := range ve {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// WriteErrorResponse writes a structured error response to the HTTP response writer
func WriteErrorResponse(w http.ResponseWriter, r *http.Request, apiErr *APIError) {
	// Get request ID from context
	requestID := ""
	if r != nil {
		if id, ok := r.Context().Value("request_id").(string); ok {
			requestID = id
		}
	}

	// Create error response
	errorResp := ErrorResponse{
		Success:   false,
		Error:     apiErr.Message,
		Code:      apiErr.Code,
		Details:   apiErr.Details,
		RequestID: requestID,
	}

	// Set headers
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(apiErr.HTTPStatus)

	// Encode and write response
	if err := json.NewEncoder(w).Encode(errorResp); err != nil {
		// Fallback to plain text if JSON encoding fails
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, "Internal server error: %v", err)
	}

	// Log the error
	logError(r, apiErr)
}

// WriteValidationErrorResponse writes a validation error response
func WriteValidationErrorResponse(w http.ResponseWriter, r *http.Request, validationErrors ValidationErrors) {
	apiErr := ErrValidationFailed.WithDetails(validationErrors)
	WriteErrorResponse(w, r, apiErr)
}

// HandleError is a centralized error handler that determines the appropriate response
func HandleError(w http.ResponseWriter, r *http.Request, err error) {
	if err == nil {
		return
	}

	// Check if it's already an APIError
	if apiErr, ok := IsAPIError(err); ok {
		WriteErrorResponse(w, r, apiErr)
		return
	}

	// Check for specific error types and convert them
	var apiErr *APIError

	switch {
	case strings.Contains(err.Error(), "timeout"):
		apiErr = WrapError(err, ErrTimeout)
	case strings.Contains(err.Error(), "not found"):
		apiErr = WrapError(err, ErrFileNotFound)
	case strings.Contains(err.Error(), "invalid format"):
		apiErr = WrapError(err, ErrInvalidImageFormat)
	case strings.Contains(err.Error(), "too large"):
		apiErr = WrapError(err, ErrFileTooLarge)
	case strings.Contains(err.Error(), "storage"):
		apiErr = WrapError(err, ErrStorageFailed)
	case strings.Contains(err.Error(), "memory"):
		apiErr = WrapError(err, ErrInsufficientMemory)
	default:
		// Generic internal server error
		apiErr = WrapError(err, ErrInternalServer)
	}

	WriteErrorResponse(w, r, apiErr)
}

// logError logs the error with appropriate context
func logError(r *http.Request, apiErr *APIError) {
	logger := logging.GetLogger()
	
	fields := logging.Fields{
		"error_code":    apiErr.Code,
		"error_message": apiErr.Message,
		"http_status":   apiErr.HTTPStatus,
	}

	if r != nil {
		fields["method"] = r.Method
		fields["path"] = r.URL.Path
		fields["user_agent"] = r.UserAgent()
		fields["remote_addr"] = r.RemoteAddr
		
		if requestID, ok := r.Context().Value("request_id").(string); ok {
			fields["request_id"] = requestID
		}
	}

	if apiErr.Details != nil {
		fields["error_details"] = apiErr.Details
	}

	// Add caller information for server errors
	if apiErr.HTTPStatus >= 500 {
		if pc, file, line, ok := runtime.Caller(2); ok {
			fields["caller"] = fmt.Sprintf("%s:%d", file, line)
			if fn := runtime.FuncForPC(pc); fn != nil {
				fields["function"] = fn.Name()
			}
		}
	}

	// Log at appropriate level
	if apiErr.HTTPStatus >= 500 {
		logger.WithFields(fields).Error("Server error occurred")
	} else if apiErr.HTTPStatus >= 400 {
		logger.WithFields(fields).Warn("Client error occurred")
	} else {
		logger.WithFields(fields).Info("Error handled")
	}
}

// RecoverFromPanic recovers from a panic and converts it to an APIError
func RecoverFromPanic() *APIError {
	if r := recover(); r != nil {
		logger := logging.GetLogger()
		
		// Get stack trace
		buf := make([]byte, 4096)
		n := runtime.Stack(buf, false)
		stackTrace := string(buf[:n])
		
		logger.WithFields(logging.Fields{
			"panic":      r,
			"stack_trace": stackTrace,
		}).Error("Panic recovered")
		
		return ErrInternalServer.WithDetails(map[string]interface{}{
			"panic": fmt.Sprintf("%v", r),
			"recovered": true,
		})
	}
	return nil
}

// ValidateImageFormat validates if the content type is a supported image format
func ValidateImageFormat(contentType string) error {
	supportedFormats := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
	}

	for _, format := range supportedFormats {
		if strings.EqualFold(contentType, format) {
			return nil
		}
	}

	return ErrInvalidImageFormat.WithDetails(map[string]interface{}{
		"provided_format":   contentType,
		"supported_formats": supportedFormats,
	})
}

// ValidateFileSize validates if the file size is within limits
func ValidateFileSize(size, maxSize int64) error {
	if size > maxSize {
		return ErrFileTooLarge.WithDetails(map[string]interface{}{
			"file_size": size,
			"max_size":  maxSize,
			"size_mb":   float64(size) / (1024 * 1024),
			"max_mb":    float64(maxSize) / (1024 * 1024),
		})
	}
	return nil
}

// ValidateRequired validates that required fields are not empty
func ValidateRequired(fields map[string]string) ValidationErrors {
	var errors ValidationErrors
	
	for field, value := range fields {
		if strings.TrimSpace(value) == "" {
			errors = append(errors, ValidationError{
				Field:   field,
				Message: "This field is required",
				Value:   value,
			})
		}
	}
	
	return errors
}

// ValidateURL validates URL format
func ValidateURL(url string) error {
	if url == "" {
		return nil // Allow empty URLs if not required
	}
	
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return NewAPIError("INVALID_URL", "URL must start with http:// or https://", http.StatusBadRequest)
	}
	
	return nil
}

// ValidateQRSize validates QR code size parameters
func ValidateQRSize(size int) error {
	if size < 64 || size > 2048 {
		return NewAPIError("INVALID_QR_SIZE", "QR code size must be between 64 and 2048 pixels", http.StatusBadRequest).WithDetails(map[string]interface{}{
			"provided_size": size,
			"min_size":      64,
			"max_size":      2048,
		})
	}
	return nil
}

// IsRetryableError checks if an error is retryable
func IsRetryableError(err error) bool {
	if apiErr, ok := IsAPIError(err); ok {
		// Retry on server errors and timeouts, but not on client errors
		return apiErr.HTTPStatus >= 500 || apiErr.Code == "TIMEOUT"
	}
	
	// Check for common retryable error patterns
	errorStr := strings.ToLower(err.Error())
	retryablePatterns := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"temporary failure",
		"service unavailable",
	}
	
	for _, pattern := range retryablePatterns {
		if strings.Contains(errorStr, pattern) {
			return true
		}
	}
	
	return false
}

// GetErrorCode extracts error code from an error
func GetErrorCode(err error) string {
	if apiErr, ok := IsAPIError(err); ok {
		return apiErr.Code
	}
	return "UNKNOWN_ERROR"
}

// GetHTTPStatus extracts HTTP status from an error
func GetHTTPStatus(err error) int {
	if apiErr, ok := IsAPIError(err); ok {
		return apiErr.HTTPStatus
	}
	return http.StatusInternalServerError
}