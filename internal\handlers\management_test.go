package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/interfaces"
)

// MockManagementMetadataManager extends MockMetadataManager for management testing
type MockManagementMetadataManager struct {
	*MockMetadataManager
	imagesData map[int][]interfaces.ImageMetadata
}

// NewMockManagementMetadataManager creates a new mock metadata manager for management testing
func NewMockManagementMetadataManager() *MockManagementMetadataManager {
	return &MockManagementMetadataManager{
		MockMetadataManager: &MockMetadataManager{},
		imagesData: make(map[int][]interfaces.ImageMetadata),
	}
}

// GetImagesForCleanup returns images for a specific folder
func (m *MockManagementMetadataManager) GetImagesForCleanup(folderNum int, maxAge time.Duration, limit int) ([]interfaces.ImageMetadata, error) {
	if m.shouldFailGetImagesForCleanup {
		return nil, m.getImagesError
	}

	if images, exists := m.imagesData[folderNum]; exists {
		if limit > 0 && len(images) > limit {
			return images[:limit], nil
		}
		return images, nil
	}

	return []interfaces.ImageMetadata{}, nil
}

// AddTestImages adds test images to a specific folder
func (m *MockManagementMetadataManager) AddTestImages(folderNum int, images []interfaces.ImageMetadata) {
	m.imagesData[folderNum] = images
}

// MockManagementStorageManager extends MockStorageManager for management testing
type MockManagementStorageManager struct {
	*MockStorageManager
}

// NewMockManagementStorageManager creates a new mock storage manager for management testing
func NewMockManagementStorageManager() *MockManagementStorageManager {
	return &MockManagementStorageManager{
		MockStorageManager: &MockStorageManager{},
	}
}

// GetFolderFromID determines folder number from image ID
func (m *MockManagementStorageManager) GetFolderFromID(id string) int {
	// Simple hash-based folder assignment for testing
	hash := 0
	for _, char := range id {
		hash += int(char)
	}
	return (hash % 1000) + 1
}

func TestNewManagementHandler(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{
		Cleanup: struct {
			MaxFolders   int `yaml:"max_folders"`
			BatchSize    int `yaml:"batch_size"`
			MaxAgeHours  int `yaml:"max_age_hours"`
		}{
			MaxFolders: 1000,
			BatchSize:  100,
			MaxAgeHours: 24,
		},
	}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	if handler == nil {
		t.Fatal("Expected handler to be created, got nil")
	}
	if handler.metadataManager != metadataManager {
		t.Error("Expected metadata manager to be set")
	}
	if handler.storageManager != storageManager {
		t.Error("Expected storage manager to be set")
	}
	if handler.config != cfg {
		t.Error("Expected config to be set")
	}
}

func TestManagementHandler_ListImages_Success(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{
		Cleanup: struct {
			MaxFolders   int `yaml:"max_folders"`
			BatchSize    int `yaml:"batch_size"`
			MaxAgeHours  int `yaml:"max_age_hours"`
		}{
			MaxFolders: 3, // Limit for testing
			BatchSize:  100,
			MaxAgeHours: 24,
		},
	}

	// Add test images to different folders
	testImages1 := []interfaces.ImageMetadata{
		{
			ImagePath:    "images/1/abc123.jpg",
			OriginalName: "test1.jpg",
			FileSize:     1024,
			ContentType:  "image/jpeg",
			CreatedAt:    time.Now().Add(-1 * time.Hour),
			LastAccessed: time.Now(),
			Location:     "local",
		},
	}
	testImages2 := []interfaces.ImageMetadata{
		{
			ImagePath:    "images/2/def456.png",
			OriginalName: "test2.png",
			FileSize:     2048,
			ContentType:  "image/png",
			CreatedAt:    time.Now().Add(-2 * time.Hour),
			LastAccessed: time.Now().Add(-30 * time.Minute),
			Location:     "local",
		},
	}

	metadataManager.AddTestImages(1, testImages1)
	metadataManager.AddTestImages(2, testImages2)

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodGet, "/images", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response ListImagesResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
	if len(response.Images) != 2 {
		t.Errorf("Expected 2 images, got %d", len(response.Images))
	}
	if response.Pagination.TotalItems != 2 {
		t.Errorf("Expected total items 2, got %d", response.Pagination.TotalItems)
	}
	if response.Pagination.CurrentPage != 1 {
		t.Errorf("Expected current page 1, got %d", response.Pagination.CurrentPage)
	}
}

func TestManagementHandler_ListImages_Pagination(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{
		Cleanup: struct {
			MaxFolders   int `yaml:"max_folders"`
			BatchSize    int `yaml:"batch_size"`
			MaxAgeHours  int `yaml:"max_age_hours"`
		}{
			MaxFolders: 1,
			BatchSize:  100,
			MaxAgeHours: 24,
		},
	}

	// Add 5 test images
	testImages := make([]interfaces.ImageMetadata, 5)
	for i := 0; i < 5; i++ {
		testImages[i] = interfaces.ImageMetadata{
			ImagePath:    fmt.Sprintf("images/1/test%d.jpg", i),
			OriginalName: fmt.Sprintf("test%d.jpg", i),
			FileSize:     int64(1024 * (i + 1)),
			ContentType:  "image/jpeg",
			CreatedAt:    time.Now().Add(-time.Duration(i) * time.Hour),
			LastAccessed: time.Now(),
			Location:     "local",
		}
	}
	metadataManager.AddTestImages(1, testImages)

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	// Test first page with page_size=2
	req := httptest.NewRequest(http.MethodGet, "/images?page=1&page_size=2", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response ListImagesResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if len(response.Images) != 2 {
		t.Errorf("Expected 2 images on first page, got %d", len(response.Images))
	}
	if response.Pagination.CurrentPage != 1 {
		t.Errorf("Expected current page 1, got %d", response.Pagination.CurrentPage)
	}
	if response.Pagination.TotalPages != 3 {
		t.Errorf("Expected total pages 3, got %d", response.Pagination.TotalPages)
	}
	if !response.Pagination.HasNext {
		t.Error("Expected has_next to be true")
	}
	if response.Pagination.HasPrevious {
		t.Error("Expected has_previous to be false")
	}
}

func TestManagementHandler_ListImages_InvalidPagination(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	tests := []struct {
		name        string
		queryString string
	}{
		{"invalid page", "?page=0"},
		{"invalid page_size", "?page_size=0"},
		{"page_size too large", "?page_size=101"},
		{"non-numeric page", "?page=abc"},
		{"non-numeric page_size", "?page_size=xyz"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/images"+tt.queryString, nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			if w.Code != http.StatusBadRequest {
				t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
			}
		})
	}
}

func TestManagementHandler_GetImage_Success(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	// Add test image
	testImage := interfaces.ImageMetadata{
		ImagePath:    "images/1/abc123.jpg",
		OriginalName: "test.jpg",
		FileSize:     1024,
		ContentType:  "image/jpeg",
		CreatedAt:    time.Now().Add(-1 * time.Hour),
		LastAccessed: time.Now(),
		Location:     "local",
	}
	// Calculate the correct folder for the ID "abc123"
	folderNum := storageManager.GetFolderFromID("abc123")
	metadataManager.AddTestImages(folderNum, []interfaces.ImageMetadata{testImage})

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodGet, "/images/abc123", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response GetImageResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
	if response.ImageInfo.ID != "abc123" {
		t.Errorf("Expected image ID abc123, got %s", response.ImageInfo.ID)
	}
	if response.ImageInfo.OriginalName != "test.jpg" {
		t.Errorf("Expected original name test.jpg, got %s", response.ImageInfo.OriginalName)
	}
}

func TestManagementHandler_GetImage_NotFound(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodGet, "/images/nonexistent", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status %d, got %d", http.StatusNotFound, w.Code)
	}

	var response APIError
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if response.Code != 404 {
		t.Errorf("Expected error code 404, got %d", response.Code)
	}
}

func TestManagementHandler_DeleteImage_Success(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	// Add test image
	testImage := interfaces.ImageMetadata{
		ImagePath:    "images/1/abc123.jpg",
		OriginalName: "test.jpg",
		FileSize:     1024,
		ContentType:  "image/jpeg",
		CreatedAt:    time.Now().Add(-1 * time.Hour),
		LastAccessed: time.Now(),
		Location:     "local",
	}
	// Calculate the correct folder for the ID "abc123"
	folderNum := storageManager.GetFolderFromID("abc123")
	metadataManager.AddTestImages(folderNum, []interfaces.ImageMetadata{testImage})

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodDelete, "/images/abc123", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response DeleteImageResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
	if response.ImageID != "abc123" {
		t.Errorf("Expected image ID abc123, got %s", response.ImageID)
	}
}

func TestManagementHandler_DeleteImage_NotFound(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodDelete, "/images/nonexistent", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status %d, got %d", http.StatusNotFound, w.Code)
	}
}

func TestManagementHandler_DeleteImage_StorageFailure(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	storageManager.shouldFailDelete = true
	storageManager.deleteError = fmt.Errorf("storage error")
	cfg := &config.Config{}

	// Add test image - need to add to correct folder based on ID
	testImage := interfaces.ImageMetadata{
		ImagePath:    "images/1/abc123.jpg",
		OriginalName: "test.jpg",
		FileSize:     1024,
		ContentType:  "image/jpeg",
		CreatedAt:    time.Now(),
		LastAccessed: time.Now(),
		Location:     "local",
	}
	// Calculate the correct folder for the ID "abc123"
	folderNum := storageManager.GetFolderFromID("abc123")
	metadataManager.AddTestImages(folderNum, []interfaces.ImageMetadata{testImage})

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	req := httptest.NewRequest(http.MethodDelete, "/images/abc123", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
	}
}

func TestManagementHandler_MethodNotAllowed(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	tests := []struct {
		name   string
		method string
		path   string
	}{
		{"POST to list", http.MethodPost, "/images"},
		{"PUT to image", http.MethodPut, "/images/abc123"},
		{"PATCH to image", http.MethodPatch, "/images/abc123"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.path, nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			if w.Code != http.StatusMethodNotAllowed {
				t.Errorf("Expected status %d, got %d", http.StatusMethodNotAllowed, w.Code)
			}
		})
	}
}

func TestManagementHandler_CORS(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	// Test OPTIONS request (preflight)
	req := httptest.NewRequest(http.MethodOptions, "/images", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d for OPTIONS request, got %d", http.StatusOK, w.Code)
	}

	// Check CORS headers
	if w.Header().Get("Access-Control-Allow-Origin") != "*" {
		t.Error("Expected Access-Control-Allow-Origin header")
	}
	if w.Header().Get("Access-Control-Allow-Methods") == "" {
		t.Error("Expected Access-Control-Allow-Methods header")
	}
}

func TestManagementHandler_InvalidImageID(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	tests := []struct {
		name   string
		method string
		path   string
	}{
		{"GET empty ID", http.MethodGet, "/images/"},
		{"DELETE empty ID", http.MethodDelete, "/images/"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.path, nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			if w.Code != http.StatusBadRequest {
				t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
			}
		})
	}
}

func TestExtractIDFromPath(t *testing.T) {
	metadataManager := NewMockManagementMetadataManager()
	storageManager := NewMockManagementStorageManager()
	cfg := &config.Config{}

	handler := NewManagementHandler(metadataManager, storageManager, cfg)

	tests := []struct {
		name     string
		path     string
		expected string
	}{
		{"JPEG file", "images/123/abc123def456.jpg", "abc123def456"},
		{"PNG file", "images/456/xyz789.png", "xyz789"},
		{"No extension", "images/789/noext", "noext"},
		{"Multiple dots", "images/111/file.name.jpeg", "file.name"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.extractIDFromPath(tt.path)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}